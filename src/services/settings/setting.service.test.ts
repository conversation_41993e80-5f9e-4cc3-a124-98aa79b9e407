import axiosInstance from '@/providers/axios/axios';
import { errorService } from '@/services/error/error.service';
import { AxiosError } from 'axios';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { PREFIX, settingService } from './setting.service';

vi.mock('@/services/error/error.service');
vi.mock('@/providers/axios/axios', () => ({
  default: {
    get: vi.fn(),
    put: vi.fn(),
  },
}));

describe('Setting service', () => {
  const mockGetRes = { data: ['value'] };
  const mockPutRes = { data: { key: 'value' } };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getSettings', () => {
    it('should call axios with correct endpoint and return data', async () => {
      vi.mocked(axiosInstance.get).mockResolvedValue(mockGetRes);

      const category = 'personal';
      const result = await settingService.getSettings(category);

      expect(axiosInstance.get).toHaveBeenCalledWith(`${PREFIX}/${category}`);
      expect(result).toEqual(mockGetRes.data);
    });

    it('should call errorService when axios throw error', async () => {
      const mockErrorRes = { message: 'error', errorCode: 'mocked' };
      const mockError = new AxiosError(mockErrorRes.message);
      vi.mocked(axiosInstance.get).mockRejectedValue(mockError);
      vi.mocked(errorService.handleError).mockReturnValue(mockErrorRes);

      const category = 'personal';
      const result = await settingService.getSettings(category);

      expect(errorService.handleError).toHaveBeenCalledWith(mockError);
      expect(result).toEqual(mockErrorRes);
    });
  });

  describe('getSettingOptions', () => {
    it('should call axios with correct endpoint and return data', async () => {
      vi.mocked(axiosInstance.get).mockResolvedValue(mockGetRes);

      const category = 'personal';
      const result = await settingService.getSettingOptions(category);

      expect(axiosInstance.get).toHaveBeenCalledWith(`${PREFIX}/${category}/options`);
      expect(result).toEqual(mockGetRes.data);
    });

    it('should call errorService when axios throw error', async () => {
      const mockErrorRes = { message: 'error', errorCode: 'mocked' };
      const mockError = new AxiosError(mockErrorRes.message);
      vi.mocked(axiosInstance.get).mockRejectedValue(mockError);
      vi.mocked(errorService.handleError).mockReturnValue(mockErrorRes);

      const category = 'personal';
      const result = await settingService.getSettingOptions(category);

      expect(errorService.handleError).toHaveBeenCalledWith(mockError);
      expect(result).toEqual(mockErrorRes);
    });
  });

  describe('updatePersonalSetting', () => {
    it('should call axios with correct endpoint and return data', async () => {
      vi.mocked(axiosInstance.put).mockResolvedValue(mockPutRes);

      const formData = {
        version: 1,
        appearance: 'system',
        recordPerPage: '25',
        dateFormat: 'yyyy-mm-dd',
        timeFormat: '24',
        defaultLanguage: '123e4567-e89b-12d3-a456-426614174000',
        defaultTimezone: '123e4567-e89b-12d3-a456-426614174001',
      };
      const result = await settingService.updatePersonalSetting(formData);

      expect(axiosInstance.put).toHaveBeenCalledWith(`${PREFIX}/personal`, formData);
      expect(result).toEqual(mockPutRes.data);
    });

    it('should call errorService when axios throw error', async () => {
      const mockErrorRes = { message: 'error', errorCode: 'mocked' };
      const mockError = new AxiosError(mockErrorRes.message);
      vi.mocked(axiosInstance.put).mockRejectedValue(mockError);
      vi.mocked(errorService.handleError).mockReturnValue(mockErrorRes);

      const formData = {
        version: 1,
        appearance: 'system',
        recordPerPage: '25',
        dateFormat: 'yyyy-mm-dd',
        timeFormat: '24',
        defaultLanguage: '123e4567-e89b-12d3-a456-426614174000',
        defaultTimezone: '123e4567-e89b-12d3-a456-426614174001',
      };
      const result = await settingService.updatePersonalSetting(formData);

      expect(errorService.handleError).toHaveBeenCalledWith(mockError);
      expect(result).toEqual(mockErrorRes);
    });
  });

  describe('updateSafetySetting', () => {
    it('should call axios with correct endpoint and return data', async () => {
      vi.mocked(axiosInstance.put).mockResolvedValue(mockPutRes);

      const formData = {
        version: 1,
        sessionLifetimeHours: '8',
        passwordExpiryDays: '90',
        passwordReuseCount: '5',
        passwordMaximumAttempts: '3',
        twoFactorSessionTimeoutDays: '30',
      };
      const result = await settingService.updateSafetySetting(formData);

      expect(axiosInstance.put).toHaveBeenCalledWith(`${PREFIX}/safety`, formData);
      expect(result).toEqual(mockPutRes.data);
    });

    it('should call errorService when axios throw error', async () => {
      const mockErrorRes = { message: 'error', errorCode: 'mocked' };
      const mockError = new AxiosError(mockErrorRes.message);
      vi.mocked(axiosInstance.put).mockRejectedValue(mockError);
      vi.mocked(errorService.handleError).mockReturnValue(mockErrorRes);

      const formData = {
        version: 1,
        sessionLifetimeHours: '8',
        passwordExpiryDays: '90',
        passwordReuseCount: '5',
        passwordMaximumAttempts: '3',
        twoFactorSessionTimeoutDays: '30',
      };
      const result = await settingService.updateSafetySetting(formData);

      expect(errorService.handleError).toHaveBeenCalledWith(mockError);
      expect(result).toEqual(mockErrorRes);
    });
  });

  describe('updateThemesSetting', () => {
    it('should call axios with correct endpoint and return data', async () => {
      vi.mocked(axiosInstance.put).mockResolvedValue(mockPutRes);

      const formData = {
        version: 1,
        toastMessagePosition: 'top-right',
        alertBarPosition: 'top',
      };
      const result = await settingService.updateThemesSetting(formData);

      expect(axiosInstance.put).toHaveBeenCalledWith(`${PREFIX}/themes`, formData);
      expect(result).toEqual(mockPutRes.data);
    });

    it('should call errorService when axios throw error', async () => {
      const mockErrorRes = { message: 'error', errorCode: 'mocked' };
      const mockError = new AxiosError(mockErrorRes.message);
      vi.mocked(axiosInstance.put).mockRejectedValue(mockError);
      vi.mocked(errorService.handleError).mockReturnValue(mockErrorRes);

      const formData = {
        version: 1,
        toastMessagePosition: 'top-right',
        alertBarPosition: 'top',
      };
      const result = await settingService.updateThemesSetting(formData);

      expect(errorService.handleError).toHaveBeenCalledWith(mockError);
      expect(result).toEqual(mockErrorRes);
    });
  });
});
